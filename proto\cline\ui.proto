syntax = "proto3";

package cline;
import "cline/common.proto";
import "cline/autocomplete.proto";
option java_package = "bot.cline.proto";
option java_multiple_files = true;

// Enum for webview provider types
enum WebviewProviderType {
  SIDEBAR = 0;
  TAB = 1;
}

// Define a new message type for webview provider info
message WebviewProviderTypeRequest {
  Metadata metadata = 1;
  WebviewProviderType provider_type = 2;
}

// Enum for ClineMessage type
enum ClineMessageType {
  ASK = 0;
  SAY = 1;
}

// Enum for ClineAsk types
enum ClineAsk {
  FOLLOWUP = 0;
  PLAN_MODE_RESPOND = 1;
  COMMAND = 2;
  COMMAND_OUTPUT = 3;
  COMPLETION_RESULT = 4;
  TOOL = 5;
  API_REQ_FAILED = 6;
  RESUME_TASK = 7;
  RESUME_COMPLETED_TASK = 8;
  MISTAKE_LIMIT_REACHED = 9;
  AUTO_APPROVAL_MAX_REQ_REACHED = 10;
  BROWSER_ACTION_LAUNCH = 11;
  USE_MCP_SERVER = 12;
  NEW_TASK = 13;
  CONDENSE = 14;
  REPORT_BUG = 15;
  SUMMARIZE_TASK = 16;
}

// Enum for ClineSay types
enum ClineSay {
  TASK = 0;
  ERROR = 1;
  API_REQ_STARTED = 2;
  API_REQ_FINISHED = 3;
  TEXT = 4;
  REASONING = 5;
  COMPLETION_RESULT_SAY = 6;
  USER_FEEDBACK = 7;
  USER_FEEDBACK_DIFF = 8;
  API_REQ_RETRIED = 9;
  COMMAND_SAY = 10;
  COMMAND_OUTPUT_SAY = 11;
  TOOL_SAY = 12;
  SHELL_INTEGRATION_WARNING = 13;
  BROWSER_ACTION_LAUNCH_SAY = 14;
  BROWSER_ACTION = 15;
  BROWSER_ACTION_RESULT = 16;
  MCP_SERVER_REQUEST_STARTED = 17;
  MCP_SERVER_RESPONSE = 18;
  MCP_NOTIFICATION = 19;
  USE_MCP_SERVER_SAY = 20;
  DIFF_ERROR = 21;
  DELETED_API_REQS = 22;
  CLINEIGNORE_ERROR = 23;
  CHECKPOINT_CREATED = 24;
  LOAD_MCP_DOCUMENTATION = 25;
  INFO = 26;
  TASK_PROGRESS = 27;
  //=== Qax ===
  QAX_TODO_UPDATE = 100;
}

// Enum for ClineSayTool tool types
enum ClineSayToolType {
  EDITED_EXISTING_FILE = 0;
  NEW_FILE_CREATED = 1;
  READ_FILE = 2;
  LIST_FILES_TOP_LEVEL = 3;
  LIST_FILES_RECURSIVE = 4;
  LIST_CODE_DEFINITION_NAMES = 5;
  SEARCH_FILES = 6;
  WEB_FETCH = 7;
}

// Enum for browser actions
enum BrowserAction {
  LAUNCH = 0;
  CLICK = 1;
  TYPE = 2;
  SCROLL_DOWN = 3;
  SCROLL_UP = 4;
  CLOSE = 5;
}

// Enum for MCP server request types
enum McpServerRequestType {
  USE_MCP_TOOL = 0;
  ACCESS_MCP_RESOURCE = 1;
}

// Enum for API request cancel reasons
enum ClineApiReqCancelReason {
  STREAMING_FAILED = 0;
  USER_CANCELLED = 1;
  RETRIES_EXHAUSTED = 2;
}

// Message for conversation history deleted range
message ConversationHistoryDeletedRange {
  int32 start_index = 1;
  int32 end_index = 2;
}

// Message for read info
message ReadInfo {
  int32 start_line = 1;
  int32 lines_read = 2;
  int32 total_lines = 3;
  bool was_truncated = 4;
}

// Message for ClineSayTool
message ClineSayTool {
  ClineSayToolType tool = 1;
  string path = 2;
  string diff = 3;
  string content = 4;
  string regex = 5;
  string file_pattern = 6;
  bool operation_is_located_in_workspace = 7;
  ReadInfo read_info = 8;
}

// Message for ClineSayBrowserAction
message ClineSayBrowserAction {
  BrowserAction action = 1;
  string coordinate = 2;
  string text = 3;
}

// Message for BrowserActionResult
message BrowserActionResult {
  string screenshot = 1;
  string logs = 2;
  string current_url = 3;
  string current_mouse_position = 4;
}

// Message for ClineAskUseMcpServer
message ClineAskUseMcpServer {
  string server_name = 1;
  McpServerRequestType type = 2;
  string tool_name = 3;
  string arguments = 4;
  string uri = 5;
}

// Message for ClinePlanModeResponse
message ClinePlanModeResponse {
  string response = 1;
  repeated string options = 2;
  string selected = 3;
}

// Message for ClineAskQuestion
message ClineAskQuestion {
  string question = 1;
  repeated string options = 2;
  string selected = 3;
}

// Message for ClineAskNewTask
message ClineAskNewTask {
  string context = 1;
}

// Message for API request retry status
message ApiReqRetryStatus {
  int32 attempt = 1;
  int32 max_attempts = 2;
  int32 delay_sec = 3;
  string error_snippet = 4;
}

// Message for ClineApiReqInfo
message ClineApiReqInfo {
  string request = 1;
  int32 tokens_in = 2;
  int32 tokens_out = 3;
  int32 cache_writes = 4;
  int32 cache_reads = 5;
  double cost = 6;
  ClineApiReqCancelReason cancel_reason = 7;
  string streaming_failed_message = 8;
  ApiReqRetryStatus retry_status = 9;
}

// Main ClineMessage type
message ClineMessage {
  int64 ts = 1;
  ClineMessageType type = 2;
  ClineAsk ask = 3;
  ClineSay say = 4;
  string text = 5;
  string reasoning = 6;
  repeated string images = 7;
  repeated string files = 8;
  bool partial = 9;
  string last_checkpoint_hash = 10;
  bool is_checkpoint_checked_out = 11;
  bool is_operation_outside_workspace = 12;
  int32 conversation_history_index = 13;
  ConversationHistoryDeletedRange conversation_history_deleted_range = 14;

  // Additional fields for specific ask/say types
  ClineSayTool say_tool = 15;
  ClineSayBrowserAction say_browser_action = 16;
  BrowserActionResult browser_action_result = 17;
  ClineAskUseMcpServer ask_use_mcp_server = 18;
  ClinePlanModeResponse plan_mode_response = 19;
  ClineAskQuestion ask_question = 20;
  ClineAskNewTask ask_new_task = 21;
  ClineApiReqInfo api_req_info = 22;
}

// UiService provides methods for managing UI interactions
service UiService {
  // Scrolls to a specific settings section in the settings view
  rpc scrollToSettings(StringRequest) returns (KeyValuePair);

  // Marks the current announcement as shown and returns whether an announcement should still be shown
  rpc onDidShowAnnouncement(EmptyRequest) returns (Boolean);

  // Subscribe to addToInput events (when user adds content via context menu)
  rpc subscribeToAddToInput(StringRequest) returns (stream String);

  // Subscribe to MCP button clicked events
  rpc subscribeToMcpButtonClicked(WebviewProviderTypeRequest) returns (stream Empty);

  // Subscribe to history button click events
  rpc subscribeToHistoryButtonClicked(WebviewProviderTypeRequest) returns (stream Empty);

  // Subscribe to chat button clicked events (when the chat button is clicked in VSCode)
  rpc subscribeToChatButtonClicked(EmptyRequest) returns (stream Empty);

  // Subscribe to account button click events
  rpc subscribeToAccountButtonClicked(EmptyRequest) returns (stream Empty);

  // Subscribe to settings button clicked events
  rpc subscribeToSettingsButtonClicked(WebviewProviderTypeRequest) returns (stream Empty);

  // Subscribe to partial message updates (streaming Cline messages as they're built)
  rpc subscribeToPartialMessage(EmptyRequest) returns (stream ClineMessage);

  // Subscribe to theme change events
  rpc subscribeToTheme(EmptyRequest) returns (stream String);

  // Initialize webview when it launches
  rpc initializeWebview(EmptyRequest) returns (Empty);

  // Subscribe to relinquish control events
  rpc subscribeToRelinquishControl(EmptyRequest) returns (stream Empty);

  // Subscribe to focus chat input events with client ID
  rpc subscribeToFocusChatInput(StringRequest) returns (stream Empty);

  // Subscribe to webview visibility change events
  rpc subscribeToDidBecomeVisible(EmptyRequest) returns (stream Empty);

  // Returns the HTML for the webview index page. This is only used by external clients, not by the vscode webview.
  rpc getWebviewHtml(EmptyRequest) returns (String);

  // Opens a URL in the default browser
  rpc openUrl(StringRequest) returns (Empty);

  // Opens the Cline walkthrough
  rpc openWalkthrough(EmptyRequest) returns (Empty);

  // Gets the current workspace name
  rpc getWorkspaceName(EmptyRequest) returns (String);

  // Sync autocomplete state with send button state
  rpc syncAutocompleteState(SyncAutocompleteStateRequest) returns (Empty);
}
