import { expect } from "chai"
import { describe, it } from "mocha"
import { constructNewFileContent as cnfc } from "./diff"

async function cnfc2(diffContent: string, originalContent: string, isFinal: boolean): Promise<string> {
	return cnfc(diffContent, originalContent, isFinal, "v2")
}

describe("constructNewFileContent", () => {
	const testCases = [
		{
			name: "empty file",
			original: "",
			diff: `------- SEARCH
=======
new content
+++++++ REPLACE`,
			expected: "new content\n",
			isFinal: true,
		},
		{
			name: "malformed search - mixed symbols",
			original: "line1\nline2\nline3",
			diff: `<<-- SEARCH
line2
=======
replaced
+++++++ REPLACE`,
			shouldThrow: true,
		},
		{
			name: "malformed search - insufficient dashes",
			original: "line1\nline2\nline3",
			diff: `-- SEARCH
line2
=======
replaced
+++++++ REPLACE`,
			shouldThrow: true,
		},
		{
			name: "malformed search - missing space",
			original: "line1\nline2\nline3",
			diff: `-------SEARCH
line2
=======
replaced
+++++++ REPLACE`,
			shouldThrow: true,
		},
		{
			name: "exact match replacement",
			original: "line1\nline2\nline3",
			diff: `------- SEARCH
line2
=======
replaced
+++++++ REPLACE`,
			expected: "line1\nreplaced\nline3",
			isFinal: true,
		},
		{
			name: "line-trimmed match replacement",
			original: "line1\n line2 \nline3",
			diff: `------- SEARCH
line2
=======
replaced
+++++++ REPLACE`,
			expected: "line1\nreplaced\nline3",
			isFinal: true,
		},
		{
			name: "block anchor match replacement",
			original: "line1\nstart\nmiddle\nend\nline5",
			diff: `------- SEARCH
start
middle
end
=======
replaced
+++++++ REPLACE`,
			expected: "line1\nreplaced\nline5",
			isFinal: true,
		},
		{
			name: "incremental processing",
			original: "line1\nline2\nline3",
			diff: [
				`------- SEARCH
line2
=======`,
				"replaced\n",
				"+++++++ REPLACE",
			].join("\n"),
			expected: "line1\nreplaced\n\nline3",
			isFinal: true,
		},
		{
			name: "final chunk with remaining content",
			original: "line1\nline2\nline3",
			diff: `------- SEARCH
line2
=======
replaced
+++++++ REPLACE`,
			expected: "line1\nreplaced\nline3",
			isFinal: true,
		},
		{
			name: "multiple ordered replacements",
			original: "First\nSecond\nThird\nFourth",
			diff: `------- SEARCH
First
=======
1st
+++++++ REPLACE

------- SEARCH
Third
=======
3rd
+++++++ REPLACE`,
			expected: "1st\nSecond\n3rd\nFourth",
			isFinal: true,
		},
		{
			name: "replace then delete",
			original: "line1\nline2\nline3\nline4",
			diff: `------- SEARCH
line2
=======
replaced
+++++++ REPLACE

------- SEARCH
line4
=======
+++++++ REPLACE`,
			expected: "line1\nreplaced\nline3\n",
			isFinal: true,
		},
		{
			name: "delete then replace",
			original: "line1\nline2\nline3\nline4",
			diff: `------- SEARCH
line1
=======
+++++++ REPLACE

------- SEARCH
line3
=======
replaced
+++++++ REPLACE`,
			expected: "line2\nreplaced\nline4",
			isFinal: true,
		},
		{
			name: "malformed diff - missing separator",
			original: "line1\nline2\nline3",
			diff: `------- SEARCH
line2
+++++++ REPLACE
replaced`,
			shouldThrow: true,
		},
		{
			name: "malformed diff - trailing space on separator",
			original: "line1\nline2\nline3",
			diff: `------- SEARCH
line2
======= 
replaced
+++++++ REPLACE`,
			shouldThrow: true,
		},
		{
			name: "malformed diff - double replace markers",
			original: "line1\nline2\nline3",
			diff: `------- SEARCH
line2
+++++++ REPLACE
first replacement
+++++++ REPLACE`,
			shouldThrow: true,
		},
		{
			name: "malformed diff - malformed separator with dashes",
			original: "line1\nline2\nline3",
			diff: `------- SEARCH
line2
------- =======
replaced
+++++++ REPLACE`,
			shouldThrow: true,
		},
	]
	//.filter(({name}) => name === "multiple ordered replacements")
	//.filter(({name}) => name === "delete then replace")
	testCases.forEach(({ name, original, diff, expected, isFinal, shouldThrow }) => {
		it(`should handle ${name} case correctly`, async () => {
			if (shouldThrow) {
				try {
					await cnfc(diff, original, isFinal ?? true)
					expect.fail("Expected an error to be thrown")
				} catch (err) {
					expect(err).to.be.an("error")
				}

				try {
					await cnfc2(diff, original, isFinal ?? true)
					expect.fail("Expected an error to be thrown")
				} catch (err) {
					expect(err).to.be.an("error")
				}
			} else {
				const result1 = await cnfc(diff, original, isFinal ?? true)
				const result2 = await cnfc2(diff, original, isFinal ?? true)
				const _equal = result1 === result2
				const _equal2 = result1 === expected
				// Verify both implementations produce same result
				expect(result1).to.equal(result2)

				// Verify result matches expected
				expect(result1).to.equal(expected)
			}
		})
	})

	it("should throw error when no match found", async () => {
		const original = "line1\nline2\nline3"
		const diff = `------- SEARCH
non-existent
=======
replaced
+++++++ REPLACE`

		try {
			await cnfc(diff, original, true)
			expect.fail("Expected an error to be thrown")
		} catch (err) {
			expect(err).to.be.an("error")
		}

		try {
			await cnfc2(diff, original, true)
			expect.fail("Expected an error to be thrown")
		} catch (err) {
			expect(err).to.be.an("error")
		}
	})

	it("should handle missing final REPLACE marker when isFinal is true", async () => {
		const original = "line1\nline2\nline3"
		const diff = `------- SEARCH
line2
=======
replaced`
		// Note: missing +++++++ REPLACE marker

		const result1 = await cnfc(diff, original, true) // isFinal = true

		// Should still work and replace line2 with "replaced"
		const expected = "line1\nreplaced\nline3"

		expect(result1).to.equal(expected)
	})

	it("should handle missing final REPLACE marker with multiple lines of replacement", async () => {
		const original = "function test() {\n\tconst a = 1;\n\treturn a;\n}"
		const diff = `------- SEARCH
	const a = 1;
	return a;
=======
	const a = 42;
	console.log('updated');
	return a;`
		// Note: missing +++++++ REPLACE marker

		const result1 = await cnfc(diff, original, true) // isFinal = true
		const expected = "function test() {\n\tconst a = 42;\n\tconsole.log('updated');\n\treturn a;\n}"

		expect(result1).to.equal(expected)
	})

	// 	it("should NOT process incomplete replacement when isFinal is false", async () => {
	// 		const original = "line1\nline2\nline3"
	// 		const diff = `------- SEARCH
	// line2
	// =======
	// replaced`
	// 		// Note: missing +++++++ REPLACE marker AND isFinal = false

	// 		const result1 = await cnfc(diff, original, false) // isFinal = false

	// 		// Should not make any changes since the block is incomplete
	// 		const expected = "line1\nline2\nline3"

	// 		expect(result1).to.equal(expected)
	// 	})
})

// Test cases for out-of-order search/replace blocks

describe("Diff Format Out of Order Cases", () => {
	it("should handle out-of-order replacements with different positions", async () => {
		const isFinal = true
		const original = "first\nsecond\nthird\nfourth\n"
		const diff = `------- SEARCH
fourth
=======
new fourth
+++++++ REPLACE
------- SEARCH
second
=======
new second
+++++++ REPLACE`
		const result1 = await cnfc(diff, original, isFinal)
		const expectedResult = "first\nnew second\nthird\nnew fourth\n"
		expect(result1).to.equal(expectedResult)
	})

	it("should handle multiple out-of-order replacements", async () => {
		const isFinal = true
		const original = "one\ntwo\nthree\nfour\nfive\n"
		const diff = `------- SEARCH
four
=======
fourth
+++++++ REPLACE
------- SEARCH
two
=======
second
+++++++ REPLACE
------- SEARCH
five
=======
fifth
+++++++ REPLACE`
		const result1 = await cnfc(diff, original, isFinal)
		const expectedResult = "one\nsecond\nthree\nfourth\nfifth\n"
		expect(result1).to.equal(expectedResult)
	})

	it("should handle out-of-order replacements with indentation", async () => {
		const isFinal = true
		const original = "function test() {\n\tconst a = 1;\n\tconst b = 2;\n\tconst c = 3;\n\n}"
		const diff = `------- SEARCH
	const c = 3;
=======
	const c = 30;
+++++++ REPLACE
------- SEARCH
	const a = 1;
=======
	const a = 10;
+++++++ REPLACE`
		const result1 = await cnfc(diff, original, isFinal)
		const expectedResult = "function test() {\n\tconst a = 10;\n\tconst b = 2;\n\tconst c = 30;\n\n}"
		expect(result1).to.equal(expectedResult)
	})

	it("should handle out-of-order replacements with empty lines", async () => {
		const isFinal = true
		const original = "header\n\nbody\n\nfooter\n"
		const diff = `------- SEARCH
footer
=======
new footer
+++++++ REPLACE
------- SEARCH

body

=======
new body content
+++++++ REPLACE`
		const result1 = await cnfc(diff, original, isFinal)
		const expectedResult = "header\nnew body content\nnew footer\n"
		expect(result1).to.equal(expectedResult)
	})

	describe("Alternative Format Support", () => {
		it("should handle alternative SEARCH/REPLACE format", async () => {
			const original = `function hello() {
    console.log("Hello, World!");
}`
			const diff = `------- SEARCH
function hello() {
    console.log("Hello, World!");
}
------- REPLACE
function hello() {
    console.log("Hello, Universe!");
}`
			const result = await cnfc2(diff, original, true)
			const expected = `function hello() {
    console.log("Hello, Universe!");
}\n`
			expect(result).to.equal(expected)
		})

		it("should handle alternative format in V1", async () => {
			const original = `function hello() {
    console.log("Hello, World!");
}`
			const diff = `------- SEARCH
function hello() {
    console.log("Hello, World!");
}
------- REPLACE
function hello() {
    console.log("Hello, Universe!");
}`
			const result = await cnfc(diff, original, true, "v1")
			const expected = `function hello() {
    console.log("Hello, Universe!");
}\n`
			expect(result).to.equal(expected)
		})

		it("should handle git-style format", async () => {
			const original = `function hello() {
    console.log("Hello, World!");
}`
			const diff = `<<<<<<< SEARCH
function hello() {
    console.log("Hello, World!");
}
=======
function hello() {
    console.log("Hello, Universe!");
}
>>>>>>> REPLACE`
			const result = await cnfc2(diff, original, true)
			const expected = `function hello() {
    console.log("Hello, Universe!");
}\n`
			expect(result).to.equal(expected)
		})

		it("should handle git-style format in V1", async () => {
			const original = `function hello() {
    console.log("Hello, World!");
}`
			const diff = `<<<<<<< SEARCH
function hello() {
    console.log("Hello, World!");
}
=======
function hello() {
    console.log("Hello, Universe!");
}
>>>>>>> REPLACE`
			const result = await cnfc(diff, original, true, "v1")
			const expected = `function hello() {
    console.log("Hello, Universe!");
}\n`
			expect(result).to.equal(expected)
		})
	})

	describe("Streaming Compatibility", () => {
		it("should not reject incomplete content when streaming", async () => {
			const original = `function hello() {
    console.log("Hello, World!");
}`
			const incompleteStandard = `------- SEARCH
function hello() {
    console.log("Hello, World!");
}
=======
function hello() {
    console.log("Hello, Universe!");`

			// Should not throw when isFinal is false
			const result = await cnfc2(incompleteStandard, original, false)
			expect(result).to.be.a("string")
		})

		it("should not reject incomplete alternative format when streaming", async () => {
			const original = `function hello() {
    console.log("Hello, World!");
}`
			const incompleteAlt = `------- SEARCH
function hello() {
    console.log("Hello, World!");
}
------- REPLACE
function hello() {`

			// Should not throw when isFinal is false
			const result = await cnfc2(incompleteAlt, original, false)
			expect(result).to.be.a("string")
		})

		it("should not reject incomplete git-style format when streaming", async () => {
			const original = `function hello() {
    console.log("Hello, World!");
}`
			const incompleteGit = `<<<<<<< SEARCH
function hello() {
    console.log("Hello, World!");
}
=======
function hello() {`

			// Should not throw when isFinal is false
			const result = await cnfc2(incompleteGit, original, false)
			expect(result).to.be.a("string")
		})

		it("should reject empty content only when final", async () => {
			const original = "some content"
			const emptyContent = ""

			// Should not throw when streaming
			const streamingResult = await cnfc2(emptyContent, original, false)
			expect(streamingResult).to.be.a("string")

			// Should throw when final
			try {
				await cnfc2(emptyContent, original, true)
				expect.fail("Should have thrown an error")
			} catch (error: any) {
				expect(error.message).to.include("Empty diff content detected")
			}
		})
	})

	describe("Invalid Format Detection", () => {
		it("should reject REPLACE without SEARCH in alternative format (V1)", async () => {
			const original = "some content"
			const invalidDiff = `------- REPLACE
new content`

			try {
				await cnfc(invalidDiff, original, true, "v1")
				expect.fail("Should have thrown an error")
			} catch (error: any) {
				expect(error.message).to.include("Found '------- REPLACE' without a preceding '------- SEARCH' block")
			}
		})

		it("should reject REPLACE without SEARCH in alternative format (V2)", async () => {
			const original = "some content"
			const invalidDiff = `------- REPLACE
new content`

			try {
				await cnfc2(invalidDiff, original, true)
				expect.fail("Should have thrown an error")
			} catch (error: any) {
				expect(error.message).to.include("Found '------- REPLACE' without a preceding '------- SEARCH' block")
			}
		})

		it("should reject multiple REPLACE blocks without SEARCH", async () => {
			const original = "some content"
			const invalidDiff = `------- REPLACE
old code
------- REPLACE
new code`

			try {
				await cnfc2(invalidDiff, original, true)
				expect.fail("Should have thrown an error")
			} catch (error: any) {
				expect(error.message).to.include("Found '------- REPLACE' without a preceding '------- SEARCH' block")
			}
		})

		it("should reject incomplete standard format when final", async () => {
			const original = "some content"
			const incompleteDiff = `------- SEARCH
some content
=======
new content`

			try {
				await cnfc2(incompleteDiff, original, true)
				expect.fail("Should have thrown an error")
			} catch (error: any) {
				expect(error.message).to.include("Incomplete diff format detected")
			}
		})

		it("should reject incomplete git-style format when final", async () => {
			const original = "some content"
			const incompleteDiff = `<<<<<<< SEARCH
some content
=======
new content`

			try {
				await cnfc2(incompleteDiff, original, true)
				expect.fail("Should have thrown an error")
			} catch (error: any) {
				expect(error.message).to.include("Incomplete diff format detected")
			}
		})

		it("should reject malformed SEARCH markers", async () => {
			const original = "some content"
			const malformedDiff = `<------ SEARCH
some content
=======
new content
+++++++ REPLACE`

			try {
				await cnfc2(malformedDiff, original, true)
				expect.fail("Should have thrown an error")
			} catch (error: any) {
				expect(error.message).to.include("Incomplete diff format detected")
			}
		})

		it("should reject malformed REPLACE markers", async () => {
			const original = "some content"
			const malformedDiff = `------- SEARCH
some content
=======
new content
<++++++ REPLACE`

			try {
				await cnfc2(malformedDiff, original, true)
				expect.fail("Should have thrown an error")
			} catch (error: any) {
				expect(error.message).to.include("Incomplete diff format detected")
			}
		})

		it("should reject mixed format markers", async () => {
			const original = "some content"
			const mixedDiff = `------- SEARCH
some content
=======
new content
>>>>>>> REPLACE`

			try {
				await cnfc2(mixedDiff, original, true)
				expect.fail("Should have thrown an error")
			} catch (error: any) {
				expect(error.message).to.include("Incomplete diff format detected")
			}
		})

		it("should reject separator without proper format", async () => {
			const original = "some content"
			const invalidSeparator = `------- SEARCH
some content
==== ===
new content
+++++++ REPLACE`

			try {
				await cnfc2(invalidSeparator, original, true)
				expect.fail("Should have thrown an error")
			} catch (error: any) {
				expect(error.message).to.include("Incomplete diff format detected")
			}
		})

		it("should handle whitespace-only content when final", async () => {
			const original = "some content"
			const whitespaceOnly = "   \n  \t  \n   "

			try {
				await cnfc2(whitespaceOnly, original, true)
				expect.fail("Should have thrown an error")
			} catch (error: any) {
				expect(error.message).to.include("Empty diff content detected")
			}
		})

		it("should allow whitespace-only content when streaming", async () => {
			const original = "some content"
			const whitespaceOnly = "   \n  \t  \n   "

			// Should not throw when streaming
			const result = await cnfc2(whitespaceOnly, original, false)
			expect(result).to.be.a("string")
		})

		it("should reject SEARCH block with only separator", async () => {
			const original = "some content"
			const onlySeparator = `------- SEARCH
=======
new content
+++++++ REPLACE`

			try {
				await cnfc2(onlySeparator, original, true)
				expect.fail("Should have thrown an error")
			} catch (error: any) {
				expect(error.message).to.include("Empty SEARCH block detected")
			}
		})
	})
})
