// A launch configuration that compiles the extension and then opens it inside a new window
// Use IntelliSense to learn about possible attributes.
// Hover to view descriptions of existing attributes.
// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
{
	"version": "0.2.0",
	"configurations": [
		{
			"name": "Run Extension (QAX)",
			"type": "extensionHost",
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}",
				"--disable-workspace-trust",
				"${workspaceFolder}"
			],
			"outFiles": [
				"${workspaceFolder}/dist/**/*.js"
			],
			"preLaunchTask": "${defaultBuildTask}",
			"env": {
				"IS_DEV": "true",
				"DEV_WORKSPACE_FOLDER": "${workspaceFolder}",
				"CLINE_ENVIRONMENT": "local",
				"QAX_ENVIRONMENT": "development",
				"QAX_MODE": "true"
			}
		},
		{
			"name": "Run Extension (production)",
			"type": "extensionHost",
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}",
				"--disable-workspace-trust",
				"${workspaceFolder}"
			],
			"outFiles": [
				"${workspaceFolder}/dist/**/*.js"
			],
			"preLaunchTask": "${defaultBuildTask}",
			"env": {
				"IS_DEV": "true",
				"DEV_WORKSPACE_FOLDER": "${workspaceFolder}",
				"CLINE_ENVIRONMENT": "production"
			}
		},
		{
			"name": "Run Extension (staging)",
			"type": "extensionHost",
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}",
				"--disable-workspace-trust",
				"${workspaceFolder}"
			],
			"outFiles": [
				"${workspaceFolder}/dist/**/*.js"
			],
			"preLaunchTask": "${defaultBuildTask}",
			"env": {
				"IS_DEV": "true",
				"DEV_WORKSPACE_FOLDER": "${workspaceFolder}",
				"CLINE_ENVIRONMENT": "staging"
			}
		},
		{
			"name": "Run Extension (local)",
			"type": "extensionHost",
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}",
				"--disable-workspace-trust",
				"${workspaceFolder}"
			],
			"outFiles": [
				"${workspaceFolder}/dist/**/*.js"
			],
			"preLaunchTask": "${defaultBuildTask}",
			"env": {
				"IS_DEV": "true",
				"DEV_WORKSPACE_FOLDER": "${workspaceFolder}",
				"CLINE_ENVIRONMENT": "local",
				"QAX_ENVIRONMENT": "development"
			}
		},
		{
			"name": "Run Extension (Fresh Install Mode)",
			"type": "extensionHost",
			"request": "launch",
			"runtimeExecutable": "${execPath}",
			"args": [
				"--user-data-dir=${workspaceFolder}/dist/tmp/user",
				"--profile-temp",
				"--sync=off",
				"--disable-extensions",
				"--extensionDevelopmentPath=${workspaceFolder}",
				"${workspaceFolder}"
			],
			"outFiles": [
				"${workspaceFolder}/dist/**/*.js"
			],
			"preLaunchTask": "clean-tmp-user",
			"internalConsoleOptions": "openOnSessionStart",
			"postDebugTask": "stop",
			"env": {
				"IS_DEV": "true",
				"TEMP_PROFILE": "true",
				"DEV_WORKSPACE_FOLDER": "${workspaceFolder}",
				"CLINE_ENVIRONMENT": "production"
			}
		},
		{
			"type": "node",
			"request": "launch",
			"name": "Run cline-core service",
			"skipFiles": [
				"<node_internals>/**"
			],
			"sourceMaps": true,
			"resolveSourceMapLocations": [
				"${workspaceFolder}/**",
				"!**/node_modules/**"
			],
			"cwd": "${workspaceFolder}/dist-standalone",
			"outFiles": [
				"${workspaceFolder}/dist-standalone/**/*.js"
			],
			"preLaunchTask": "compile-standalone",
			"env": {
				// Turns on grpc debug log.
				//"GRPC_TRACE": "all",
				//"GRPC_VERBOSITY": "DEBUG",
				"NODE_PATH": "${workspaceFolder}/dist-standalone/node_modules"
			},
			"program": "cline-core.js"
		}
	]
}
