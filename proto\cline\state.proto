syntax = "proto3";
package cline;
import "cline/common.proto";
option java_package = "bot.cline.proto";
option java_multiple_files = true;

service StateService {
  rpc getLatestState(EmptyRequest) returns (State);
  rpc updateTerminalConnectionTimeout(UpdateTerminalConnectionTimeoutRequest) returns (UpdateTerminalConnectionTimeoutResponse);
  rpc updateTerminalReuseEnabled(BooleanRequest) returns (Empty);
  rpc updateDefaultTerminalProfile(StringRequest) returns (TerminalProfileUpdateResponse);
  rpc getAvailableTerminalProfiles(EmptyRequest) returns (TerminalProfiles);
  rpc subscribeToState(EmptyRequest) returns (stream State);
  rpc toggleFavoriteModel(StringRequest) returns (Empty);
  rpc resetState(ResetStateRequest) returns (Empty);
  rpc togglePlanActModeProto(TogglePlanActModeRequest) returns (Boolean);
  rpc updateAutoApprovalSettings(AutoApprovalSettingsRequest) returns (Empty);
  rpc updateSettings(UpdateSettingsRequest) returns (Empty);
  rpc updateTelemetrySetting(TelemetrySettingRequest) returns (Empty);
  rpc setWelcomeViewCompleted(BooleanRequest) returns (Empty);
}

message State {
  string state_json = 1;
}

message TerminalProfiles {
  repeated TerminalProfile profiles = 1;
}

message TerminalProfile {
  string id = 1;
  string name = 2;
  optional string path = 3;
  optional string description = 4;
}

message TerminalProfileUpdateResponse {
  int32 closed_count = 1;
  int32 busy_terminals_count = 2;
  bool has_busy_terminals = 3;
}

message TogglePlanActModeRequest {
  Metadata metadata = 1;
  PlanActMode mode = 2;
  optional ChatContent chat_content = 3;
}

enum PlanActMode {
  PLAN = 0;
  ACT = 1;
}

enum OpenaiReasoningEffort {
  LOW = 0;
  MEDIUM = 1;
  HIGH = 2;
}

enum McpDisplayMode {
  RICH = 0;
  PLAIN = 1;
  MARKDOWN = 2;
}

message ChatContent {
  optional string message = 1;
  repeated string images = 2;
  repeated string files = 3;
}

message ResetStateRequest {
  Metadata metadata = 1;
  optional bool global = 2;
}

message AutoApprovalSettingsRequest {
  Metadata metadata = 1;
  message Actions {
    bool read_files = 1;
    bool read_files_externally = 2;
    bool edit_files = 3;
    bool edit_files_externally = 4;
    bool execute_safe_commands = 5;
    bool execute_all_commands = 6;
    bool use_browser = 7;
    bool use_mcp = 8;
  }
  int32 version = 2;
  bool enabled = 3;
  Actions actions = 4;
  int32 max_requests = 5;
  bool enable_notifications = 6;
  repeated string favorites = 7;
}

enum TelemetrySettingEnum {
  UNSET = 0;
  ENABLED = 1;
  DISABLED = 2;
}

message TelemetrySettingRequest {
  Metadata metadata = 1;
  TelemetrySettingEnum setting = 2;
}

// Message for updating settings
message UpdateSettingsRequest {
  Metadata metadata = 1;
  optional ApiConfiguration api_configuration = 2;
  optional string telemetry_setting = 3;
  optional bool plan_act_separate_models_setting = 4;
  optional bool enable_checkpoints_setting = 5;
  optional bool mcp_marketplace_enabled = 6;
  optional int32 shell_integration_timeout = 8;
  optional bool terminal_reuse_enabled = 9;
  optional bool mcp_responses_collapsed = 10;
  optional McpDisplayMode mcp_display_mode = 11;
  optional int32 terminal_output_line_limit = 12;
  optional PlanActMode mode = 13;
  optional string preferred_language = 14;
  optional OpenaiReasoningEffort openai_reasoning_effort = 15;
  optional bool strict_plan_mode_enabled = 16;
  optional FocusChainSettings focus_chain_settings = 17;
  optional bool use_auto_condense = 18;
  //=== Qax ===
  optional AutocompleteSettings autocomplete_settings = 100;
}

// Complete API Configuration message
message ApiConfiguration {
  // Global configuration fields (not mode-specific)
  optional string api_key = 1; // anthropic
  optional string cline_api_key = 2;
  optional string ulid = 3;
  optional string lite_llm_base_url = 4;
  optional string lite_llm_api_key = 5;
  optional bool lite_llm_use_prompt_cache = 6;
  optional string openai_headers = 7; // JSON string
  optional string anthropic_base_url = 8;
  optional string openrouter_api_key = 9;
  optional string openrouter_provider_sorting = 10;
  optional string aws_access_key = 11;
  optional string aws_secret_key = 12;
  optional string aws_session_token = 13;
  optional string aws_region = 14;
  optional bool aws_use_cross_region_inference = 15;
  optional bool aws_bedrock_use_prompt_cache = 16;
  optional bool aws_use_profile = 17;
  optional string aws_profile = 18;
  optional string aws_bedrock_endpoint = 19;
  optional string claude_code_path = 20;
  optional string vertex_project_id = 21;
  optional string vertex_region = 22;
  optional string openai_base_url = 23;
  optional string openai_api_key = 24;
  optional string ollama_base_url = 25;
  optional string ollama_api_options_ctx_num = 26;
  optional string lm_studio_base_url = 27;
  optional string gemini_api_key = 28;
  optional string gemini_base_url = 29;
  optional string openai_native_api_key = 30;
  optional string deep_seek_api_key = 31;
  optional string requesty_api_key = 32;
  optional string requesty_base_url = 33;
  optional string together_api_key = 34;
  optional string fireworks_api_key = 35;
  optional int32 fireworks_model_max_completion_tokens = 36;
  optional int32 fireworks_model_max_tokens = 37;
  optional string qwen_api_key = 38;
  optional string doubao_api_key = 39;
  optional string mistral_api_key = 40;
  optional string azure_api_version = 41;
  optional string qwen_api_line = 42;
  optional string nebius_api_key = 43;
  optional string asksage_api_url = 44;
  optional string asksage_api_key = 45;
  optional string xai_api_key = 46;
  optional string sambanova_api_key = 47;
  optional string cerebras_api_key = 48;
  optional int32 request_timeout_ms = 49;
  optional string sap_ai_core_client_id = 50;
  optional string sap_ai_core_client_secret = 51;
  optional string sap_ai_resource_group = 52;
  optional string sap_ai_core_token_url = 53;
  optional string sap_ai_core_base_url = 54;
  optional string moonshot_api_key = 55;
  optional string moonshot_api_line = 56;
  optional string huawei_cloud_maas_api_key = 57;
  optional string ollama_api_key = 58;
  optional string zai_api_key = 59;
  optional string zai_api_line = 60;
  optional string lm_studio_max_tokens = 61;
  optional string vercel_ai_gateway_api_key = 62;
  
  // Plan mode configurations
  optional string plan_mode_api_provider = 100;
  optional string plan_mode_api_model_id = 101;
  optional int32 plan_mode_thinking_budget_tokens = 102;
  optional string plan_mode_reasoning_effort = 103;
  optional string plan_mode_vscode_lm_model_selector = 104; // JSON string
  optional bool plan_mode_aws_bedrock_custom_selected = 105;
  optional string plan_mode_aws_bedrock_custom_model_base_id = 106;
  optional string plan_mode_openrouter_model_id = 107;
  optional string plan_mode_openrouter_model_info = 108; // JSON string
  optional string plan_mode_openai_model_id = 109;
  optional string plan_mode_openai_model_info = 110; // JSON string
  optional string plan_mode_ollama_model_id = 111;
  optional string plan_mode_lm_studio_model_id = 112;
  optional string plan_mode_lite_llm_model_id = 113;
  optional string plan_mode_lite_llm_model_info = 114; // JSON string
  optional string plan_mode_requesty_model_id = 115;
  optional string plan_mode_requesty_model_info = 116; // JSON string
  optional string plan_mode_together_model_id = 117;
  optional string plan_mode_fireworks_model_id = 118;
  optional string plan_mode_sap_ai_core_model_id = 119;
  optional string plan_mode_huawei_cloud_maas_model_id = 120;
  optional string plan_mode_huawei_cloud_maas_model_info = 121;
  optional string plan_mode_vercel_ai_gateway_model_id = 122;
  optional string plan_mode_vercel_ai_gateway_model_info = 123;
  
  // Act mode configurations
  optional string act_mode_api_provider = 200;
  optional string act_mode_api_model_id = 201;
  optional int32 act_mode_thinking_budget_tokens = 202;
  optional string act_mode_reasoning_effort = 203;
  optional string act_mode_vscode_lm_model_selector = 204; // JSON string
  optional bool act_mode_aws_bedrock_custom_selected = 205;
  optional string act_mode_aws_bedrock_custom_model_base_id = 206;
  optional string act_mode_openrouter_model_id = 207;
  optional string act_mode_openrouter_model_info = 208; // JSON string
  optional string act_mode_openai_model_id = 209;
  optional string act_mode_openai_model_info = 210; // JSON string
  optional string act_mode_ollama_model_id = 211;
  optional string act_mode_lm_studio_model_id = 212;
  optional string act_mode_lite_llm_model_id = 213;
  optional string act_mode_lite_llm_model_info = 214; // JSON string
  optional string act_mode_requesty_model_id = 215;
  optional string act_mode_requesty_model_info = 216; // JSON string
  optional string act_mode_together_model_id = 217;
  optional string act_mode_fireworks_model_id = 218;
  optional string act_mode_sap_ai_core_model_id = 219;
  optional string act_mode_huawei_cloud_maas_model_id = 220;
  optional string act_mode_huawei_cloud_maas_model_info = 221;
  optional string act_mode_vercel_ai_gateway_model_id = 222;
  optional string act_mode_vercel_ai_gateway_model_info = 223;
  
  // Favorited model IDs
  repeated string favorited_model_ids = 300;

  // Extension fields for Bedrock Api Keys
  optional string aws_authentication = 301;
  optional string aws_bedrock_api_key = 302;

  optional string cline_account_id = 303;
}

message UpdateTerminalConnectionTimeoutRequest {
  optional int32 timeout_ms = 1;
}

message FocusChainSettings {
  bool enabled = 1;
  int32 remind_cline_interval = 2;
}

message UpdateTerminalConnectionTimeoutResponse {
  optional int32 timeout_ms = 1;
}
