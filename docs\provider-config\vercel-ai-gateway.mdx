---
title: "Vercel AI Gateway"
description: "Learn how to use Vercel AI Gateway with Cline to access a wide variety of AI models through a single unified API."
---

The AI Gateway offers a unified API to multiple model providers and gives you the ability to set budgets, monitor usage, load-balance requests, and manage fallbacks.

Useful Links:
- [AI Gateway Team Dashboard](https://vercel.com/d?to=%2F%5Bteam%5D%2F%7E%2Fai&title=AI+Gateway+Overview+Tab)
- [AI Gateway Landing Page](https://vercel.com/ai-gateway)

### Getting an API Key

1. **Sign Up/Sign In:** Go to [vercel.com](https://vercel.com/) and create an account or sign in.
2. **Get an API Key:** From the Vercel dashboard, click the AI Gateway tab. Then click **API keys** on the left side bar and hit **Create key**.
3. **Copy the Key:** Copy the API key.

For more on Vercel AI Gateway authentication, see [Authentication](https://vercel.com/docs/ai-gateway/authentication)

### Supported Models

Vercel AI Gateway supports a large and growing number of models from various providers. <PERSON><PERSON> automatically fetches the list of available models from the Gateway API. Refer to the [Vercel AI Gateway Models page](https://vercel.com/ai-gateway/models) for the complete list.

### Configuration in Cline

1. **Open Cline Settings:** Click the settings icon (⚙️) in the Cline panel.
2. **Select Provider:** Choose "Vercel AI Gateway" from the "API Provider" dropdown.
3. **Enter API Key:** Paste your Vercel AI Gateway API key into the "Vercel AI Gateway API Key" field.
4. **Select Model:** Choose your desired model from the "Model" dropdown, or enter a custom model ID if the model list doesn't load automatically.

### Tips and Notes

-   **Model Selection:** The AI Gateway offers a wide range of models. Experiment to find the best one for your needs.
-   **Pricing:** Vercel charges based on the underlying model's pricing. See the [AI Gateway Pricing page](https://vercel.com/docs/ai-gateway/pricing) for details.
