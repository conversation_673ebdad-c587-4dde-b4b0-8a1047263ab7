import { Empty } from "@shared/proto/cline/common"
import { NewTaskRequest } from "@shared/proto/cline/task"
import { QaxMemoryService } from "../../../qax/services/memory/QaxMemoryService"
import { Controller } from ".."

/**
 * Creates a new task with the given text and optional images
 * @param controller The controller instance
 * @param request The new task request containing text and optional images
 * @returns Empty response
 */
export async function newTask(controller: Controller, request: NewTaskRequest): Promise<Empty> {
	// Create the task first, then process memory after initialization
	await controller.initTask(request.text, request.images, request.files)

	// Process the first user message for memory after task and memory service are initialized
	if (request.text) {
		QaxMemoryService.processUserInputIfReady(request.text)
	}

	return Empty.create()
}
